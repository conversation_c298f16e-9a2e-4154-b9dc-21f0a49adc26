import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Platform,
  Linking,
} from "react-native";
import { Icon } from "@rneui/themed";
import * as WebBrowser from "expo-web-browser";
import { Picker } from "@react-native-picker/picker";
import PropTypes from "prop-types";
import YoutubeDestination from "./YoutubeDestination";
import ProfileDestination from "./ProfileDestination";
import AuthorizationAlertModal from "./AuthorizationAlertModal";
import axios from "axios";
import Constants from "expo-constants";
import { UserContext } from "../../context/UserContext";
import {
  saveYoutubeSelections,
  loadYoutubeSelections,
  findMatchingChannel,
  createDefaultSelection,
  extractPlaylistId,
} from "../../utils/storageUtils";


// No longer need dummyPlaylists as we're using real data from the API

// New WebView component
function AuthorizationWebView({ url, onClose }) {
  const openBrowser = async () => {
    await WebBrowser.openBrowserAsync(url);
    onClose();
  };

  useEffect(() => {
    openBrowser();
  }, []);

  return null; // No need to render anything
}

// Add DestinationConfigurations
const DestinationConfigurations = [
  { id: "1", name: "Config1", configuration: {} },
  { id: "2", name: "Config2", configuration: {} },
  { id: "3", name: "Config3", configuration: {} },
];

export default function DestinationSelector({
  onComplete,
  initialDestinations,
  wizardData = {},
  buttonContainerStyle,
}) {
  const { user } = useContext(UserContext);
  const [destinations, setDestinations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize with saved destinations if available
  const [selectedDestinations, setSelectedDestinations] = useState(() => {
    if (initialDestinations && initialDestinations.length > 0) {
      return initialDestinations.map((dest) => dest.id);
    }
    return [];
  });

  // Initialize playlist selections from initial data
  const [playlistSelections, setPlaylistSelections] = useState(() => {
    if (initialDestinations && initialDestinations.length > 0) {
      const initialSelections = {};
      initialDestinations.forEach((dest) => {
        if (dest.playlist) {
          initialSelections[dest.id] = dest.playlist;
        }
      });
      return initialSelections;
    }
    return {};
  });
  const [showPlaylistSection, setShowPlaylistSection] = useState(null); // Store which destination is showing playlist
  const [showAuthorization, setShowAuthorization] = useState(false);
  const [currentAuthURL, setCurrentAuthURL] = useState(null);
  const [showAuthAlert, setShowAuthAlert] = useState(false);
  const [currentDestId, setCurrentDestId] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState("blank");
  const [showSaveConfigDialog, setShowSaveConfigDialog] = useState(false);
  const [authorizedChannels, setAuthorizedChannels] = useState([]);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [error, setError] = useState(null);
  const [authorizedProfiles, setAuthorizedProfiles] = useState([]);
  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);
  const [profileSelections, setProfileSelections] = useState({});
  const [excludeSelections, setExcludeSelections] = useState({});
  const [allDestinationsSelected, setAllDestinationsSelected] = useState(false);

  // State for long video warning dialog
  const [showLongVideoWarning, setShowLongVideoWarning] = useState(false);
  const [ineligibleChannel, setIneligibleChannel] = useState(null);
  const [newConfigName, setNewConfigName] = useState(""); // Added missing state

  // Log initial state for debugging
  useEffect(() => {
    console.log("DestinationSelector initializing with:", {
      initialDestinations,
      selectedDestinations,
      playlistSelections,
    });
  }, []);

  // Fetch destinations from the backend
  const fetchDestinations = async () => {
    if (!user?.email) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/get_destinations/${user.email}`
      );

      if (response.status === 200 && response.data) {
        console.log("Fetched destinations:", response.data);
        setDestinations(response.data);
      }
    } catch (error) {
      console.error("Error fetching destinations:", error);
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else if (error.response?.status) {
        setError(`Server error: ${error.response.status}`);
      } else {
        setError("Failed to load destinations. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDestinations();
  }, [user]);

  // Fetch authorized profiles for TikTok, Facebook, Instagram
  const fetchAuthorizedProfiles = async () => {
    if (!user?.email) return;

    setIsLoadingProfiles(true);
    try {
      const response = await axios.get(
        `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/authorized_profiles/${user.email}`
      );

      if (response.status === 200 && response.data) {
        setAuthorizedProfiles(response.data);
        console.log('Fetched authorized profiles:', response.data);
      }
    } catch (error) {
      console.error("Error fetching authorized profiles:", error);
    } finally {
      setIsLoadingProfiles(false);
    }
  };

  useEffect(() => {
    fetchAuthorizedProfiles();
  }, [user]);

  // Fetch authorized channels when component mounts
  useEffect(() => {
    const fetchAuthorizedChannels = async () => {
      if (!user?.email) return;

      setIsLoadingChannels(true);
      try {
        const response = await axios.get(
          `${Constants.expoConfig.extra.SANATANA_SERVICE_DOMAIN}/youtube/authorized_channels/${user.email}`
        );

        if (response.status === 200 && response.data) {
          const channels = response.data;
          setAuthorizedChannels(channels);
          console.log('Fetched authorized channels:', channels);

          // After fetching channels, restore saved selections or set defaults
          if (channels.length > 0) {
            const savedSelections = loadYoutubeSelections();
            console.log('Loaded saved selections:', savedSelections);

            // Process each YouTube destination
            const youtubeDestinations = ["youtube_video", "youtube_shorts"];
            const newSelections = { ...playlistSelections };
            let selectionChanged = false;

            youtubeDestinations.forEach((destId) => {
              // Skip if not in selected destinations
              if (!selectedDestinations.includes(destId)) {
                console.log(`Skipping ${destId} - not in selected destinations`);
                return;
              }

              console.log(`Processing ${destId} for default selection`);

              // If we already have a selection for this destination, check if it's valid
              if (newSelections[destId]) {
                console.log(`Existing selection found for ${destId}:`, newSelections[destId]);
                const channelName = newSelections[destId].channel_name;
                const matchingChannel = findMatchingChannel(
                  channels,
                  channelName
                );

                // If the channel doesn't exist anymore, we need to update the selection
                if (!matchingChannel) {
                  console.log(`Channel ${channelName} not found, creating default selection`);
                  const defaultSelection = createDefaultSelection(
                    destId,
                    channels
                  );
                  if (defaultSelection) {
                    newSelections[destId] = defaultSelection;
                    selectionChanged = true;
                  }
                }
              }
              // If we don't have a selection yet, try to restore from saved selections
              else if (savedSelections && savedSelections[destId]) {
                console.log(`No current selection, but found saved selection for ${destId}`);
                const savedSelection = savedSelections[destId];
                const channelName = savedSelection.channel_name;
                const matchingChannel = findMatchingChannel(
                  channels,
                  channelName
                );

                // If the saved channel exists, use the saved selection
                if (matchingChannel) {
                  console.log(`Matching channel found for saved selection: ${channelName}`);
                  // For existing playlist type, verify the playlist still exists
                  if (savedSelection.type === "existing") {
                    // Extract playlist ID if it's a URL
                    const savedPlaylistId = savedSelection.id.includes(
                      "youtube.com"
                    )
                      ? extractPlaylistId(savedSelection.id)
                      : savedSelection.id;

                    const playlistExists = matchingChannel.playlists.some(
                      (p) => {
                        // Get the playlist ID from either id or url
                        const playlistId =
                          p.id || (p.url ? extractPlaylistId(p.url) : null);
                        return playlistId === savedPlaylistId;
                      }
                    );

                    if (playlistExists) {
                      console.log(`Playlist ${savedPlaylistId} exists, using saved selection`);
                      newSelections[destId] = savedSelection;
                      selectionChanged = true;
                    } else {
                      // Playlist doesn't exist anymore, use default
                      console.log(`Playlist ${savedPlaylistId} not found, creating default selection`);
                      newSelections[destId] = createDefaultSelection(
                        destId,
                        channels
                      );
                      selectionChanged = true;
                    }
                  } else {
                    // For other selection types (none, auto, new), just use the saved selection
                    console.log(`Using saved selection of type ${savedSelection.type}`);
                    newSelections[destId] = savedSelection;
                    selectionChanged = true;
                  }
                } else {
                  // Channel doesn't exist anymore, use default
                  console.log(`Channel ${channelName} not found, creating default selection`);
                  newSelections[destId] = createDefaultSelection(
                    destId,
                    channels
                  );
                  selectionChanged = true;
                }
              }
              // If no saved selection, create default
              else {
                console.log(`No selection found for ${destId}, creating default selection`);
                const defaultSelection = createDefaultSelection(
                  destId,
                  channels
                );
                if (defaultSelection) {
                  console.log(`Created default selection for ${destId}:`, defaultSelection);
                  newSelections[destId] = defaultSelection;
                  selectionChanged = true;
                }
              }
            });

            // Update state if any selections changed
            if (selectionChanged) {
              console.log('Updating playlist selections with:', newSelections);
              setPlaylistSelections(newSelections);
              saveYoutubeSelections(newSelections);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching authorized channels:", error);
      } finally {
        setIsLoadingChannels(false);
      }
    };

    fetchAuthorizedChannels();
  }, [user, selectedDestinations]);

  const handlePlaylistSelect = (destId, selection) => {
    setPlaylistSelections((prevSelections) => {
      const newSelections = {
        ...prevSelections,
        [destId]: selection,
      };

      // Save selections to localStorage
      saveYoutubeSelections(newSelections);

      return newSelections;
    });
    // Optional: Close the playlist section after selection
    setShowPlaylistSection(null);
  };

  const handleAuthorization = async (destId) => {
    // Store the current destination ID
    setCurrentDestId(destId);

    // Show the authorization alert modal
    setShowAuthAlert(true);
  };

  // Function to handle proceeding to SetupMedia
  // This is now just a placeholder since navigation is handled directly in the AuthorizationAlertModal
  const handleProceedToSetup = () => {
    // Close the alert (though this is now handled in the modal)
    setShowAuthAlert(false);

    // Log that we're proceeding
    console.log("Proceeding to setup from DestinationSelector");
  };

  const handleCloseAuthorization = async () => {
    setShowAuthorization(false);

    // Dummy API call
    try {
      const response = await fetch("https://api.example.com/authorize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ destination: currentAuthURL }),
      });

      if (response.ok) {
        // Update authorization status (you'll need to implement this)
        // For now, just log success
        console.log("Authorization successful");
      }
    } catch (error) {
      console.error("Authorization error:", error);
    }
  };

  const handleToggleDestination = (destId) => {
    // Handle "All Authorized Destinations" selection
    if (destId === "all_authorized") {
      const newAllSelected = !allDestinationsSelected;
      setAllDestinationsSelected(newAllSelected);

      if (newAllSelected) {
        // Select all authorized destinations
        const authorizedDestIds = destinations.filter(dest => dest.authorized && dest.enabled).map(dest => dest.id);
        setSelectedDestinations(authorizedDestIds);

        // Initialize exclude selections for all authorized destinations
        const newExcludeSelections = {};
        authorizedDestIds.forEach(id => {
          newExcludeSelections[id] = false;
        });
        setExcludeSelections(newExcludeSelections);
      } else {
        // Deselect all destinations
        setSelectedDestinations([]);
        setExcludeSelections({});
      }
      return;
    }

    const dest = destinations.find((d) => d.id === destId);

    // Check authorization first for TikTok, Facebook, Instagram
    if (dest && !dest.authorized && (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post")) {
      handleAuthorization(destId);
      return;
    }

    // Check authorization for YouTube (existing logic)
    if (dest && !dest.authorized) {
      handleAuthorization(destId);
      return;
    }

    setSelectedDestinations((prev) => {
      const newDestinations = prev.includes(destId)
        ? prev.filter((id) => id !== destId)
        : [...prev, destId];

      // If adding a YouTube destination and we have authorized channels,
      // set default selection if none exists
      if (
        !prev.includes(destId) &&
        (destId === "youtube_shorts" || destId === "youtube_video") &&
        authorizedChannels.length > 0
      ) {
        // Use setTimeout to ensure this runs after state update
        setTimeout(() => {
          setPlaylistSelections((prevSelections) => {
            console.log("Setting default selection for", destId);
            console.log("Current selections:", prevSelections);
            console.log("Authorized channels:", authorizedChannels);

            // Always create a default selection when a destination is added
            const defaultSelection = createDefaultSelection(
              destId,
              authorizedChannels
            );

            if (defaultSelection) {
              console.log("Created default selection:", defaultSelection);
              const newSelections = {
                ...prevSelections,
                [destId]: defaultSelection,
              };

              // Save to localStorage
              saveYoutubeSelections(newSelections);

              return newSelections;
            }
            return prevSelections;
          });
        }, 0);
      }

      // If adding a TikTok, Facebook, or Instagram destination and we have authorized profiles,
      // set default profile selection if none exists
      if (
        !prev.includes(destId) &&
        (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") &&
        authorizedProfiles.length > 0
      ) {
        setTimeout(() => {
          setProfileSelections((prevSelections) => {
            if (!prevSelections[destId]) {
              const platform = destId.split('_')[0];
              const platformProfiles = authorizedProfiles.filter(profile =>
                profile.platform?.toLowerCase() === platform.toLowerCase() && profile.active === 1
              );

              if (platformProfiles.length > 0) {
                const newSelections = {
                  ...prevSelections,
                  [destId]: [platformProfiles[0].platform_userid]
                };
                return newSelections;
              }
            }
            return prevSelections;
          });
        }, 0);
      }

      return newDestinations;
    });
  };

  const getPlaylistDisplay = (destId) => {
    const selection = playlistSelections[destId];
    if (!selection) return null;

    if (selection.type === "none") {
      return `${selection.channel_name} - No playlist`;
    } else if (selection.type === "auto") {
      return `${selection.channel_name} - Auto playlist`;
    } else if (selection.type === "new") {
      return `${selection.channel_name} - New: ${selection.name}`;
    } else {
      return `${selection.channel_name} - ${selection.name}`;
    }
  };

  const getProfileDisplay = (destId) => {
    const selection = profileSelections[destId];
    if (!selection || selection.length === 0) return "No Profile Selected";

    return selection.join(", ");
  };

  const handleProfileSelect = (destId, selectedProfiles) => {
    setProfileSelections(prev => ({
      ...prev,
      [destId]: selectedProfiles
    }));
  };

  const handleExcludeToggle = (destId) => {
    setExcludeSelections(prev => ({
      ...prev,
      [destId]: !prev[destId]
    }));
  };

  const processPlaylistSelection = (selection) => {
    if (!selection) return null;

    // Find the channel to get its ID and other details
    const channel = selection.channel_name
      ? authorizedChannels.find(
        (c) => c.channel_name === selection.channel_name
      )
      : null;

    // Always include channel_name and channel_id if they exist
    const baseSelection = {};

    if (selection.channel_name) {
      baseSelection.channel_name = selection.channel_name;
    }

    if (channel && channel.channel_id) {
      baseSelection.channel_id = channel.channel_id;
    }

    if (selection.type === "auto") {
      return { ...baseSelection, type: "auto" };
    } else if (selection.type === "none") {
      return { ...baseSelection, type: "none" };
    } else if (selection.type === "new") {
      return { ...baseSelection, type: "new", name: selection.name };
    } else if (selection.type === "existing") {
      let playlistName = selection.name;

      // If we have the channel, try to find the playlist to get its name
      if (channel && channel.playlists) {
        const playlist = channel.playlists.find((p) => {
          const playlistId = p.id || (p.url ? extractPlaylistId(p.url) : null);
          return playlistId === selection.id;
        });

        if (playlist) {
          playlistName = playlist.name;
        }
      }

      return {
        ...baseSelection,
        type: "existing",
        id: selection.id,
        name: playlistName || selection.name || `Playlist ${selection.id}`,
      };
    }
    return null;
  };

  const handleConfigChange = (configId) => {
    setSelectedConfig(configId);
    if (configId !== "blank") {
      // Dummy method to initialize destinations
      console.log("Initializing destinations for config:", configId);
      // Here you would normally load the configuration and set the state
    }
  };

  const checkForConfigChanges = () => {
    // Dummy method to check for changes
    console.log("Checking for configuration changes...");
    return true; // For now, always return true to show the dialog
  };

  const handleCreateNewConfig = () => {
    if (newConfigName.trim()) {
      // Dummy method to create new config
      console.log("Creating new configuration:", newConfigName);
      // Here you would normally save the configuration
      setShowSaveConfigDialog(false);
      setNewConfigName("");
      handleContinue();
    }
  };

  const handleContinue = () => {
    if (selectedConfig !== "blank" && checkForConfigChanges()) {
      setShowSaveConfigDialog(true);
      return;
    }

    // Check if the video is longer than 15 minutes
    const isVideoLongerThan15Min = wizardData?.duration > 900;

    // If the video is longer than 15 minutes, check if all selected YouTube channels are eligible
    if (isVideoLongerThan15Min) {
      for (const destId of selectedDestinations) {
        // Only check YouTube destinations
        if (!destId.startsWith('youtube')) continue;

        const selection = playlistSelections[destId];
        if (!selection || !selection.channel_name) continue;

        // Find the channel in authorizedChannels
        const channel = authorizedChannels.find(ch => ch.channel_name === selection.channel_name);

        // If channel is not eligible for long uploads, show warning
        if (channel && channel.longUploadsStatus !== 'eligible') {
          setIneligibleChannel(channel);
          setShowLongVideoWarning(true);
          return;
        }
      }
    }

    // Proceed with normal continue logic
    let destinations = [];

    if (allDestinationsSelected) {
      // Handle "All Authorized Destinations" selection
      const authorizedDests = allDestinations.filter(dest => dest.authorized && dest.enabled && dest.id !== "all_authorized");

      for (const dest of authorizedDests) {
        const destId = dest.id;

        // Skip if excluded
        if (excludeSelections[destId]) continue;

        if (destId === "youtube_video" || destId === "youtube_shorts") {
          // Handle YouTube destinations
          const selection = playlistSelections[destId];
          destinations.push({
            id: destId,
            name: dest.title,
            playlist: processPlaylistSelection(selection),
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          });
        } else if (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") {
          // Handle social media destinations with profiles
          const profiles = profileSelections[destId] || [];
          profiles.forEach(profileId => {
            destinations.push({
              id: destId,
              name: dest.title,
              platform_userid: profileId,
              ...(wizardData?.categories && { categories: [...wizardData.categories] })
            });
          });
        }
      }
    } else {
      // Handle individual destination selections
      destinations = selectedDestinations.map((destId) => {
        const dest = allDestinations.find((d) => d.id === destId);

        if (destId === "youtube_video" || destId === "youtube_shorts") {
          const selection = playlistSelections[destId];
          return {
            id: destId,
            name: dest ? dest.title : destId,
            playlist: processPlaylistSelection(selection),
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          };
        } else if (destId === "tiktok_video" || destId === "facebook_post" || destId === "instagram_post") {
          const profiles = profileSelections[destId] || [];
          return profiles.map(profileId => ({
            id: destId,
            name: dest ? dest.title : destId,
            platform_userid: profileId,
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          }));
        } else {
          return {
            id: destId,
            name: dest ? dest.title : destId,
            ...(wizardData?.categories && { categories: [...wizardData.categories] })
          };
        }
      }).flat();
    }

    // Log the final destinations array for debugging
    console.log("Final destinations with categories:", destinations);

    onComplete({ destinations });
  };

  // Filter destinations by enabled status
  const enabledDestinations = destinations.filter((dest) => dest.enabled);
  const disabledDestinations = destinations.filter((dest) => !dest.enabled);

  // Add "All Authorized Destinations" at the top if any destination is authorized
  const hasAuthorizedDestinations = enabledDestinations.some(dest => dest.authorized);
  const allDestinations = [];

  if (hasAuthorizedDestinations) {
    allDestinations.push({
      id: "all_authorized",
      title: "All Authorized Destinations",
      friendly_name: "All Platforms",
      icon: "globe",
      type: "font-awesome",
      enabled: true,
      authorized: true
    });
  }

  allDestinations.push(...enabledDestinations, ...disabledDestinations);

  // Check if any YouTube destinations are selected
  // const hasSelectedYouTube = selectedDestinations.some((id) =>
  //   id.startsWith("youtube")
  // );

  // Check if a video is longer than 15 minutes (900 seconds)
  const isLongVideo = wizardData?.duration > 900;

  // Check if a channel is eligible for long uploads
  const isChannelEligibleForLongUploads = (channelName) => {
    // Find the channel in the authorizedChannels array
    const channel = authorizedChannels.find(ch => ch.channel_name === channelName);
    return channel?.longUploadsStatus === "eligible";
  };

  // Check if a destination has a valid playlist selection
  // const isValidSelection = (destId) => {
  //   const selection = playlistSelections[destId];
  //   return (
  //     selection?.type === "none" ||
  //     selection?.type === "auto" ||
  //     selection?.type === "new" ||
  //     selection?.id
  //   ); // for existing playlist
  // };

  const renderDestination = (dest) => {
    const isSelected = selectedDestinations.includes(dest.id) || (allDestinationsSelected && dest.id !== "all_authorized");
    const isYouTube = dest.id.startsWith("youtube");
    const isSocialMedia = dest.id === "tiktok_video" || dest.id === "facebook_post" || dest.id === "instagram_post";
    const isAllAuthorized = dest.id === "all_authorized";
    const playlistSelection = playlistSelections[dest.id];
    const profileSelection = profileSelections[dest.id];
    const isExcluded = excludeSelections[dest.id];
    const canAuthorize = dest.enabled;

    // Gray out individual destinations when "All Authorized Destinations" is selected
    const isGrayedOut = allDestinationsSelected && !isAllAuthorized;

    return (
      <React.Fragment key={dest.id}>
        <TouchableOpacity
          onPress={() => dest.enabled && handleToggleDestination(dest.id)}
          className={`flex-col md:flex-row p-4 mb-3 rounded-lg border ${
            !dest.enabled
              ? "border-gray-200 bg-gray-50 opacity-50"
              : isGrayedOut
              ? "border-gray-300 bg-gray-100 opacity-75"
              : isSelected
              ? "border-blue-500 bg-blue-50"
              : "border-gray-200 bg-white"
            }`}
          disabled={!dest.enabled || isGrayedOut}
          style={{ flexWrap: "wrap" }}
        >
          {/* Top row with icon and title - always visible */}
          <View className="flex-row items-center w-full">
            <Icon
              name={dest.icon}
              type={dest.type || "font-awesome"}
              size={24}
              color={
                dest.enabled ? (isSelected ? "#3B82F6" : "#4B5563") : "#9CA3AF"
              }
            />
            <View className="flex-1 ml-3">
              <Text
                className={`${dest.enabled
                    ? isSelected
                      ? "text-blue-600 font-medium"
                      : "text-gray-700"
                    : "text-gray-400"
                  }`}
              >
                {dest.title}
              </Text>

              {/* Show authorization status for enabled destinations */}
              {dest.enabled && (
                <View className="flex-row items-center mt-1">
                  {dest.authorized ? (
                    <View className="flex-row items-center">
                      <Text className="text-xs text-green-600 mr-1">
                        Authorized
                      </Text>
                      <Icon
                        name="check-circle"
                        type="font-awesome"
                        size={12}
                        color="#16A34A"
                      />
                    </View>
                  ) : (
                    <Text className="text-xs text-orange-500">
                      Your {dest.friendly_name} is not Authorized. Select to Authorize.
                    </Text>
                  )}
                </View>
              )}

              {isSelected && isYouTube && (
                <Text className="text-sm text-gray-500 mt-1 flex-wrap">
                  {playlistSelection
                    ? getPlaylistDisplay(dest.id)
                    : "No Channel Selected"}
                </Text>
              )}

              {isSelected && isSocialMedia && (
                <Text className="text-sm text-gray-500 mt-1 flex-wrap">
                  {getProfileDisplay(dest.id)}
                </Text>
              )}

              {allDestinationsSelected && dest.authorized && !isAllAuthorized && (
                <View className="flex-row items-center mt-2">
                  <TouchableOpacity
                    onPress={() => handleExcludeToggle(dest.id)}
                    className="flex-row items-center"
                  >
                    <Icon
                      name={isExcluded ? "check-square" : "square"}
                      type="font-awesome"
                      size={16}
                      color={isExcluded ? "#3B82F6" : "#6B7280"}
                    />
                    <Text className="ml-2 text-sm text-gray-600">Exclude</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>

          {/* Second row with authorization and playlist selection - only visible when selected */}
          {isSelected && (
            <View className="flex-row justify-end items-center w-full mt-3 md:mt-0 md:ml-auto md:w-auto">
              {/* Authorization Button (only show if not authorized) */}
              {!dest.authorized && (
                <View className="mr-4">
                  <TouchableOpacity
                    onPress={() => handleAuthorization(dest.id)}
                    className="flex-row items-center"
                    disabled={!canAuthorize}
                  >
                    <Text
                      className={`text-sm ${canAuthorize ? "text-blue-600" : "text-gray-400"
                        }`}
                    >
                      Authorize Media
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Playlist Selection Button */}
              {isYouTube && (
                <TouchableOpacity
                  onPress={() =>
                    setShowPlaylistSection(
                      showPlaylistSection === dest.id ? null : dest.id
                    )
                  }
                  className="flex-row items-center"
                >
                  <Text className="text-sm text-blue-600 mr-2">
                    {playlistSelection ? "Change playlist" : "Select playlist"}
                  </Text>
                  <Icon
                    name={
                      showPlaylistSection === dest.id
                        ? "chevron-up"
                        : "chevron-down"
                    }
                    type="font-awesome"
                    size={12}
                    color="#2563EB"
                  />
                </TouchableOpacity>
              )}

              {/* Profile Selection Button */}
              {isSocialMedia && dest.authorized && (
                <TouchableOpacity
                  onPress={() =>
                    setShowPlaylistSection(
                      showPlaylistSection === dest.id ? null : dest.id
                    )
                  }
                  className="flex-row items-center"
                >
                  <Text className="text-sm text-blue-600 mr-2">
                    {profileSelection && profileSelection.length > 0 ? "Change profiles" : "Select profiles"}
                  </Text>
                  <Icon
                    name={
                      showPlaylistSection === dest.id
                        ? "chevron-up"
                        : "chevron-down"
                    }
                    type="font-awesome"
                    size={12}
                    color="#2563EB"
                  />
                </TouchableOpacity>
              )}
            </View>
          )}

          {!dest.enabled && (
            <View className="w-full md:w-auto md:ml-auto">
              <Text className="text-xs text-gray-400 mt-2">Coming soon</Text>
            </View>
          )}
        </TouchableOpacity>

        {isYouTube && isSelected && showPlaylistSection === dest.id && (
          <YoutubeDestination
            dest={dest} // Pass the destination object
            playlistSelections_temp={playlistSelections} // Pass the state object
            onSelectPlaylist={handlePlaylistSelect} // Pass the handler function
            authorizedChannels={authorizedChannels} // Pass the authorized channels
            isLoadingChannels={isLoadingChannels} // Pass loading state
          />
        )}

        {isSocialMedia && isSelected && showPlaylistSection === dest.id && (
          <ProfileDestination
            dest={dest}
            profileSelections={profileSelections}
            onSelectProfile={handleProfileSelect}
            authorizedProfiles={authorizedProfiles}
            isLoadingProfiles={isLoadingProfiles}
          />
        )}

        {allDestinationsSelected && dest.authorized && !isAllAuthorized && isExcluded && showPlaylistSection === dest.id && (
          <ProfileDestination
            dest={dest}
            profileSelections={profileSelections}
            onSelectProfile={handleProfileSelect}
            authorizedProfiles={authorizedProfiles}
            isLoadingProfiles={isLoadingProfiles}
            excludeMode={true}
            onExcludeToggle={handleExcludeToggle}
            isExcluded={isExcluded}
          />
        )}
      </React.Fragment>
    );
  };

  return (
    <View
      className="flex-1 flex-col"
      style={{ height: "100%", display: "flex", flexDirection: "column" }}
    >
      {/* Header with Configuration Picker */}
      <View className="px-5 pt-5">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-2xl font-bold text-gray-800">
            Select Destinations
          </Text>
          <View className="my-4 w-full px-4">
            {/* <View className="items-end">
    <Text className="text-sm font-semibold text-gray-700 text-right mb-1 max-w-[160px]">
      Select Pre-Configured Destinations
    </Text>

    <View className="border border-gray-200 rounded-lg w-40 opacity-50">
      <Picker
        enabled={false}
        selectedValue={selectedConfig}
        onValueChange={handleConfigChange}
        style={{ height: 40 }}
      >
        <Picker.Item label="Blank Configuration" value="blank" />
        {DestinationConfigurations.map((config) => (
          <Picker.Item
            key={config.id}
            label={config.name}
            value={config.id}
          />
        ))}
      </Picker>
    </View>

    <Text className="text-xs text-gray-500 mt-1 text-right w-40">
      Coming soon
    </Text>
  </View> */}
          </View>
        </View>
        <Text className="text-gray-600 mb-6">
          Choose where you want to upload your content
        </Text>
      </View>

      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{ paddingBottom: 95 }}
      >
        {error ? (
          <View className="flex-1 justify-center items-center py-10">
            <Icon name="exclamation-circle" type="font-awesome" size={24} color="#EF4444" />
            <Text className="text-red-600 text-base mt-2 mb-4 text-center">{error}</Text>
            <TouchableOpacity
              className="bg-red-500 px-4 py-2 rounded-lg"
              onPress={() => {
                setError(null);
                fetchDestinations();
              }}
            >
              <Text className="text-white font-medium">Retry</Text>
            </TouchableOpacity>
          </View>
        ) : isLoading ? (
          <View className="flex-1 justify-center items-center py-10">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="mt-4 text-gray-600">Loading destinations...</Text>
          </View>
        ) : allDestinations.length > 0 ? (
          allDestinations.map(renderDestination)
        ) : (
          <View className="flex-1 justify-center items-center py-10">
            <Text className="text-gray-600">No destinations available</Text>
          </View>
        )}
      </ScrollView>

      <View
        className="bg-white"
        style={
          buttonContainerStyle || {
            position: "sticky",
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 10,
          }
        }
      >
        <TouchableOpacity
          onPress={handleContinue}
          disabled={selectedDestinations.length === 0}
          className={`rounded-lg py-4 ${selectedDestinations.length > 0 ? "bg-blue-600" : "bg-gray-300"
            }`}
          style={{ marginHorizontal: 20 }}
        >
          <Text className="text-white text-center font-semibold">Continue</Text>
        </TouchableOpacity>
      </View>
      {showAuthorization && (
        <AuthorizationWebView
          url={currentAuthURL}
          onClose={handleCloseAuthorization}
          onAuthorized={() => { }}
        />
      )}

      {/* Authorization Alert Modal */}
      <AuthorizationAlertModal
        visible={showAuthAlert}
        onClose={() => setShowAuthAlert(false)}
        onProceed={handleProceedToSetup}
        destinationTitle={
          currentDestId
            ? allDestinations.find((d) => d.id === currentDestId)?.friendly_name || ""
            : ""
        }
        platformId={
          // Map destination IDs to their corresponding media platform IDs
          currentDestId === "youtube_video" 
            ? "youtube"
            : currentDestId === "tiktok_video"
            ? "tiktok"
            : currentDestId === "facebook_post"
            ? "facebook"
            : currentDestId === "instagram_post"
            ? "instagram"
            : currentDestId
        }
      />

      {/* Configuration Dialog */}
      {showSaveConfigDialog && (
        <View className="absolute inset-0 bg-black bg-opacity-50 justify-center items-center z-50">
          <View className="bg-white p-5 rounded-lg w-11/12">
            <Text className="text-lg font-bold mb-3">Save Configuration</Text>
            <TextInput
              value={newConfigName}
              onChangeText={setNewConfigName}
              placeholder="Enter configuration name"
              className="border border-gray-200 rounded-lg p-2 mb-3"
            />
            <View className="flex-row justify-end">
              <TouchableOpacity
                onPress={() => setShowSaveConfigDialog(false)}
                className="px-4 py-2 mr-2"
              >
                <Text className="text-gray-600">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleCreateNewConfig}
                className="bg-blue-600 px-4 py-2 rounded-lg"
              >
                <Text className="text-white">Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Long Video Warning Dialog */}
      {showLongVideoWarning && (
        <View className="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <View className="bg-white p-5 rounded-lg w-11/12 max-w-md">
            <Text className="text-lg font-bold mb-3">Channel Not Eligible for Long Videos</Text>

            <Text className="text-red-600 font-medium mb-2">
              Warning: This video is longer than 15 minutes
            </Text>

            <Text className="mb-4">
              The channel <Text className="font-bold">{ineligibleChannel?.channel_name}</Text> is not eligible to upload videos longer than 15 minutes.
            </Text>

            <Text className="mb-4">
              To upload longer videos, you need to verify this channel on YouTube.
            </Text>

            <View className="bg-gray-100 p-3 rounded-lg mb-4">
              <Text className="text-sm">Steps to verify your channel:</Text>
              <Text className="text-sm">1. Go to YouTube Studio</Text>
              <Text className="text-sm">2. Switch to the {ineligibleChannel?.channel_name} channel</Text>
              <Text className="text-sm">3. Visit <Text className="text-blue-600 underline" onPress={() => Linking.openURL('https://www.youtube.com/verify')}>youtube.com/verify</Text></Text>
              <Text className="text-sm">4. Follow the verification steps</Text>
            </View>

            <View className="flex-row justify-end">
              <TouchableOpacity
                onPress={() => setShowLongVideoWarning(false)}
                className="bg-gray-200 px-4 py-2 rounded-lg mr-2"
              >
                <Text className="text-gray-800">Select Different Channel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

// PropTypes validation

DestinationSelector.propTypes = {
  onComplete: PropTypes.func.isRequired,
  initialDestinations: PropTypes.array,
  wizardData: PropTypes.object,
  buttonContainerStyle: PropTypes.object,
};

DestinationSelector.defaultProps = {
  initialDestinations: [],
  wizardData: {},
  buttonContainerStyle: null,
};
