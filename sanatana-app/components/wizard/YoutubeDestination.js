// YoutubeDestination.js
import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import PropTypes from "prop-types";
import { Icon } from "@rneui/themed";

// Note: We no longer need the hardcoded data as it comes from props now

// Renamed prop playlistSelections_temp to currentSelection for clarity
export default function YoutubeDestination({
  dest, // Pass the whole dest object or just dest.id and isShorts
  playlistSelections_temp, // Use this to derive the current selection
  onSelectPlaylist, // Pass the callback function
  authorizedChannels = [], // Channels with playlists from the API
  isLoadingChannels = false, // Loading state
}) {
  // Extract necessary info from dest prop
  const destinationId = dest.id;
  const isShorts = dest.id === "youtube_shorts";

  // Get the current selection for *this* specific destination
  const currentSelection = playlistSelections_temp[destinationId];
  const currentSelections = Array.isArray(currentSelection) ? currentSelection : (currentSelection ? [currentSelection] : []);

  // State specific to this component's UI
  const [newPlaylistInputs, setNewPlaylistInputs] = useState({});
  const [inputErrors, setInputErrors] = useState({});
  const [expandedChannelName, setExpandedChannelName] = useState(null); // State to track expanded channel
  const [showNewPlaylistForChannel, setShowNewPlaylistForChannel] = useState(null); // Track which channel is showing new playlist input

  const handleCreatePlaylist = (channelName) => {
    const trimmedName = newPlaylistInputs[channelName]?.trim() || "";
    if (trimmedName) {
      onSelectPlaylist(destinationId, {
        type: "new",
        name: trimmedName,
        channel_name: channelName,
      });

      // Clear input and error for this channel
      setNewPlaylistInputs(prev => ({
        ...prev,
        [channelName]: ""
      }));
      setInputErrors(prev => ({
        ...prev,
        [channelName]: ""
      }));

      // Hide the new playlist input for this channel
      setShowNewPlaylistForChannel(null);
    } else {
      setInputErrors(prev => ({
        ...prev,
        [channelName]: "Playlist name cannot be empty."
      }));
    }
  };

  const toggleChannelExpansion = (channelName) => {
    setExpandedChannelName(
      expandedChannelName === channelName ? null : channelName
    );
  };

  // Helper to check if a specific playlist is selected
  const isPlaylistSelected = (channel, playlist) => {
    // Extract playlist ID from URL if needed
    let playlistId = playlist.id;
    if (!playlistId && playlist.url) {
      const match = playlist.url.match(/[?&]list=([^&]+)/);
      playlistId = match ? match[1] : null;
    }

    return currentSelections.some(sel =>
      sel?.type === "existing" &&
      sel?.id === playlistId && // Using playlist ID
      sel?.channel_name === channel.channel_name
    );
  };

  return (
    <View className="bg-white rounded-lg border border-gray-200 p-1 m-1">
      {/* Header: Select Playlist */}
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-gray-700 font-medium">Select Channel and Playlist</Text>
      </View>

      {/* Channel and Playlist List */}
      <ScrollView className="max-h-64">
        {isLoadingChannels ? (
          <View className="flex items-center justify-center p-4">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-600 mt-2">
              Loading channels and playlists...
            </Text>
          </View>
        ) : authorizedChannels && authorizedChannels.length > 0 ? (
          authorizedChannels.map((channel) => (
            <View
              key={channel.channel_name || channel.channel_id}
              className="mb-2"
            >
              {/* Channel Header (Expand/Collapse Trigger) */}
              <TouchableOpacity
                onPress={() => toggleChannelExpansion(channel.channel_name)}
                className="flex-row items-center justify-between p-3 bg-gray-100 rounded-t-lg border border-gray-200"
                style={
                  expandedChannelName !== channel.channel_name
                    ? styles.roundedBottom
                    : {}
                } // Add rounded bottom if collapsed
              >
                <View className="flex-col">
                  {/* Long Upload Eligibility Indicator */}
                  {channel.longUploadsStatus === "eligible" && (
                    <Text className="text-xs text-green-600 font-medium mb-1">
                      Long Videos Eligible
                    </Text>
                  )}
                  <Text className="font-medium text-gray-800">
                    {channel.channel_name}
                  </Text>
                </View>
                <Icon
                  name={
                    expandedChannelName === channel.channel_name
                      ? "chevron-up"
                      : "chevron-down"
                  }
                  type="font-awesome"
                  size={16}
                  color="#6B7280"
                />
              </TouchableOpacity>

              {/* Expanded Playlist List */}
              {expandedChannelName === channel.channel_name && (
                <View className="border border-t-0 border-gray-200 rounded-b-lg p-2">
                  {/* Create New Playlist Button */}
                  <TouchableOpacity
                    onPress={() => {
                      // Toggle the new playlist input for this channel
                      setShowNewPlaylistForChannel(
                        showNewPlaylistForChannel === channel.channel_name ? null : channel.channel_name
                      );
                    }}
                    className="flex-row items-center justify-between p-3 mb-3 rounded-lg border border-blue-300 bg-blue-50"
                  >
                    <View className="flex-row items-center">
                      <Icon name="plus" type="font-awesome" size={18} color="#3B82F6" />
                      <Text className="ml-2 font-medium text-blue-600">
                        Create New Playlist
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* New Playlist Input Form */}
                  {showNewPlaylistForChannel === channel.channel_name && (
                    <View className="p-3 mb-3 border border-gray-200 rounded-lg bg-white">
                      <TextInput
                        value={newPlaylistInputs[channel.channel_name] || ''}
                        onChangeText={(text) => {
                          setNewPlaylistInputs(prev => ({
                            ...prev,
                            [channel.channel_name]: text
                          }));
                          // Clear error when user types
                          setInputErrors(prev => ({
                            ...prev,
                            [channel.channel_name]: ''
                          }));
                        }}
                        placeholder="Enter playlist name"
                        className={`border rounded-lg p-2 ${
                          inputErrors[channel.channel_name] ? "border-red-300" : "border-gray-200"
                        }`}
                      />
                      {inputErrors[channel.channel_name] && (
                        <Text className="text-sm text-red-500 mt-1">{inputErrors[channel.channel_name]}</Text>
                      )}
                      <TouchableOpacity
                        onPress={() => handleCreatePlaylist(channel.channel_name)}
                        className="mt-2 bg-blue-600 rounded-lg py-2"
                      >
                        <Text className="text-white text-center">Create Playlist</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                  <TouchableOpacity
                    key={"none"}
                    onPress={() =>
                      onSelectPlaylist(destinationId, {
                        type: "none",
                        channel_name: channel.channel_name,
                      })
                    }
                    className={`flex-row items-center p-3 mb-3 rounded-lg border ${
                      currentSelections.some(sel => sel?.type === "none" && sel?.channel_name === channel.channel_name)
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200"
                    }`}
                  >
                    <Icon
                      name="times-circle"
                      type="font-awesome"
                      size={24}
                      color={
                        currentSelections.some(sel => sel?.type === "none" && sel?.channel_name === channel.channel_name)
                          ? "#3B82F6"
                          : "#6B7280"
                      }
                    />
                    <View className="ml-3 flex-1">
                      <Text
                        className={`font-medium ${
                          currentSelections.some(sel => sel?.type === "none" && sel?.channel_name === channel.channel_name)
                            ? "text-blue-600"
                            : "text-gray-700"
                        }`}
                      >
                        No Playlist
                      </Text>
                      <Text className="text-sm text-gray-500">
                        Upload without adding to a playlist
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {isShorts && (
                    <TouchableOpacity
                      key={"auto"}
                      onPress={() =>
                        onSelectPlaylist(destinationId, {
                          type: "auto",
                          channel_name: channel.channel_name,
                        })
                      }
                      className={`flex-row items-center p-3 mb-3 rounded-lg border ${
                        currentSelections.some(sel => sel?.type === "auto" && sel?.channel_name === channel.channel_name)
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200"
                      }`}
                    >
                      <Icon
                        name="magic"
                        type="font-awesome"
                        size={24}
                        color={
                          currentSelections.some(sel => sel?.type === "auto" && sel?.channel_name === channel.channel_name)
                            ? "#3B82F6"
                            : "#6B7280"
                        }
                      />
                      <View className="ml-3 flex-1">
                        <Text
                          className={`font-medium ${
                            currentSelections.some(sel => sel?.type === "auto" && sel?.channel_name === channel.channel_name)
                              ? "text-blue-600"
                              : "text-gray-700"
                          }`}
                        >
                          Auto Playlist
                        </Text>
                        <Text className="text-sm text-gray-500">
                          Creates playlist automatically with video title
                        </Text>
                      </View>
                    </TouchableOpacity>
                  )}
                  {channel.playlists && channel.playlists.length > 0 ? (
                    channel.playlists.map((playlist) => (
                      <TouchableOpacity
                        key={playlist.id || playlist.url || "existing"} // Use playlist ID or URL as key
                        onPress={() => {
                          // Extract playlist ID from URL
                          let playlistId = playlist.id;

                          // If we have a URL but no ID, extract ID from URL
                          if (!playlistId && playlist.url) {
                            // Extract playlist ID from URL (format: https://www.youtube.com/playlist?list=PLAYLIST_ID)
                            const match =
                              playlist.url.match(/[?&]list=([^&]+)/);
                            playlistId = match ? match[1] : null;
                          }

                          onSelectPlaylist(destinationId, {
                            type: "existing",
                            id: playlistId, // Use extracted playlist ID
                            name: playlist.name, // Include name for potential display elsewhere
                            channel_name: channel.channel_name, // Include channel name
                            url: playlist.url, // Keep the URL for reference
                          });
                        }}
                        className={`flex-row items-center p-3 mb-2 rounded-lg border ${
                          isPlaylistSelected(channel, playlist)
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-100" // Lighter border for inner items
                        }`}
                      >
                        <Icon
                          name="playlist-play"
                          type="material-community"
                          size={24}
                          color={
                            isPlaylistSelected(channel, playlist)
                              ? "#3B82F6"
                              : "#6B7280"
                          }
                        />
                        <View className="ml-3 flex-1">
                          <Text
                            className={`font-medium ${
                              isPlaylistSelected(channel, playlist)
                                ? "text-blue-600"
                                : "text-gray-700"
                            }`}
                          >
                            {playlist.name}
                          </Text>
                          {/* You can add playlist.videoCount here if that data becomes available */}
                          {/* <Text className="text-sm text-gray-500">{playlist.videoCount} videos</Text> */}
                        </View>
                      </TouchableOpacity>
                    ))
                  ) : (
                    <Text className="text-sm text-gray-500 p-3">
                      No playlists found for this channel.
                    </Text>
                  )}
                </View>
                )}
              </View>
            ))
          ) : (
            <Text className="text-sm text-gray-500 p-3">
              No authorized channels found.
            </Text>
          )}
        </ScrollView>
      {/* End of loading check */}

      {/* Optional: Warning messages can remain in the parent or be moved here */}
    </View>
  );
}
// Simple StyleSheet for conditional rounding
const styles = StyleSheet.create({
  roundedBottom: {
    borderBottomLeftRadius: 8, // Corresponds to rounded-lg
    borderBottomRightRadius: 8,
  },
});

// Add PropTypes validation
YoutubeDestination.propTypes = {
  dest: PropTypes.shape({
    id: PropTypes.string.isRequired,
  }).isRequired,
  playlistSelections_temp: PropTypes.object.isRequired,
  onSelectPlaylist: PropTypes.func.isRequired,
  authorizedChannels: PropTypes.array,
  isLoadingChannels: PropTypes.bool,
};
